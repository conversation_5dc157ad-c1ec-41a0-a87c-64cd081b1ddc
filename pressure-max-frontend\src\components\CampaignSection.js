import React, { useState, useEffect } from 'react';
import api, { facebookAPI } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import { Target, Plus, DollarSign, Calendar, AlertCircle, Zap } from 'lucide-react';
import TargetingDisplay from './TargetingDisplay';
import CreativeDisplay from './CreativeDisplay';
import CampaignWizard from './CampaignWizard';

const CAMPAIGN_OBJECTIVES = [
  // New OUTCOME-based objectives (recommended)
  { value: 'OUTCOME_TRAFFIC', label: 'Traffic' },
  { value: 'OUTCOME_ENGAGEMENT', label: 'Engagement' },
  { value: 'OUTCOME_LEADS', label: 'Lead Generation' },
  { value: 'OUTCOME_SALES', label: 'Sales' },
  { value: 'OUTCOME_AWARENESS', label: 'Awareness' },
  { value: 'OUTCOME_APP_PROMOTION', label: 'App Promotion' },

  // Legacy objectives (still supported but deprecated)
  { value: 'REACH', label: 'Reach (Legacy)' },
  { value: 'CONVERSIONS', label: 'Conversions (Legacy)' },
  { value: 'BRAND_AWARENESS', label: 'Brand Awareness (Legacy)' },
  { value: 'MESSAGES', label: 'Messages (Legacy)' },
  { value: 'VIDEO_VIEWS', label: 'Video Views (Legacy)' },
  { value: 'STORE_VISITS', label: 'Store Visits (Legacy)' }
];

const CampaignSection = () => {
  const { isAuthenticated } = useAuth();
  const [campaigns, setCampaigns] = useState([]);
  const [adSets, setAdSets] = useState([]);
  const [ads, setAds] = useState([]);
  const [adAccounts, setAdAccounts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showCampaignWizard, setShowCampaignWizard] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState('');
  const [activeTab, setActiveTab] = useState('campaigns');
  const [loadedData, setLoadedData] = useState({
    campaigns: false,
    adsets: false,
    ads: false
  });

  const { register, handleSubmit, reset, formState: { errors } } = useForm();

  useEffect(() => {
    if (isAuthenticated) {
      loadAdAccounts();
    }
  }, [isAuthenticated]);

  useEffect(() => {
    if (selectedAccount) {
      // Reset loaded data when account changes
      setLoadedData({
        campaigns: false,
        adsets: false,
        ads: false
      });
      // Only load campaigns by default when account is selected
      loadCampaigns();
    }
  }, [selectedAccount]);

  // Load data when tab changes (smart batching means we get everything at once)
  useEffect(() => {
    if (selectedAccount && activeTab) {
      // With smart batching, we get all data when campaigns are loaded
      // So we only need to check if campaigns are loaded
      if (!loadedData.campaigns) {
        loadCampaigns(); // This loads everything: campaigns, ad sets, and ads
      }
    }
  }, [activeTab, selectedAccount, loadedData]);

  const loadAdAccounts = async () => {
    try {
      // Use the direct Facebook API endpoint that works with the access token
      const response = await api.get('/api/v1/facebook/ad-accounts');
      setAdAccounts(response.data || []);
      if (response.data?.length > 0) {
        setSelectedAccount(response.data[0].id);
      }
    } catch (error) {
      console.error('Error loading ad accounts:', error);
      toast.error('Failed to load ad accounts');
    }
  };

  const loadCampaigns = async () => {
    if (!selectedAccount) return;

    try {
      setLoading(true);
      console.log('🚀 Loading complete account data with smart batching...');

      // Use the direct Facebook API endpoint that works with the access token
      const response = await api.get(`/api/v1/facebook/campaigns/${selectedAccount}`);
      const completeData = response.data || [];

      // Extract campaigns, ad sets, and ads from the nested structure
      setCampaigns(completeData);

      // Extract ad sets from all campaigns
      const allAdSets = completeData.flatMap(campaign =>
        (campaign.adsets || []).map(adset => ({
          ...adset,
          campaign_id: campaign.id,
          campaign_name: campaign.name
        }))
      );
      setAdSets(allAdSets);

      // Extract ads from all ad sets
      const allAds = allAdSets.flatMap(adset =>
        (adset.ads || []).map(ad => ({
          ...ad,
          adset_id: adset.id,
          adset_name: adset.name,
          campaign_id: adset.campaign_id,
          campaign_name: adset.campaign_name
        }))
      );
      setAds(allAds);

      // Mark all data as loaded since we got everything in one call
      setLoadedData({
        campaigns: true,
        adsets: true,
        ads: true
      });

      console.log('✅ Smart batching complete!', {
        campaigns: completeData.length,
        adSets: allAdSets.length,
        ads: allAds.length
      });

    } catch (error) {
      toast.error('Failed to load account data');
      setCampaigns([]);
      setAdSets([]);
      setAds([]);
    } finally {
      setLoading(false);
    }
  };

  // Note: loadAdSets and loadAds functions removed - smart batching gets everything in loadCampaigns

  const handleWizardSuccess = (result) => {
    console.log('Campaign hierarchy created:', result);
    toast.success('Campaign hierarchy created successfully!');
    setShowCampaignWizard(false);
    // Refresh campaigns data
    if (selectedAccount) {
      loadCampaigns(selectedAccount);
    }
  };

  const onCreateCampaign = async (data) => {
    try {
      setLoading(true);
      const campaignData = {
        adAccountId: selectedAccount,
        name: data.name,
        objective: data.objective,
        status: 'PAUSED', // Always start paused for safety
        specialAdCategories: data.specialAdCategories ? [data.specialAdCategories] : []
      };

      // Use the direct Facebook API endpoint that works with the access token
      await api.post('/api/v1/facebook/campaigns', campaignData);
      toast.success('Campaign created successfully!');

      // Reset form and reload campaigns
      reset();
      setShowCreateForm(false);
      loadCampaigns();
    } catch (error) {
      toast.error(error.response?.data?.message || 'Failed to create campaign');
    } finally {
      setLoading(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="campaign-section">
        <h2>Campaign Management</h2>
        <div className="auth-required">
          <AlertCircle size={20} />
          <p>Please log in to manage campaigns</p>
        </div>
      </div>
    );
  }

  if (adAccounts.length === 0) {
    return (
      <div className="campaign-section">
        <h2>Campaign Management</h2>
        <div className="no-accounts">
          <AlertCircle size={20} />
          <p>No Facebook ad accounts found. Please connect your Facebook account first.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="campaign-section">
      <h2>Campaign Management</h2>
      
      <div className="account-selector">
        <label>Select Ad Account:</label>
        <select 
          value={selectedAccount} 
          onChange={(e) => setSelectedAccount(e.target.value)}
        >
          {adAccounts.map((account) => (
            <option key={account.id} value={account.id}>
              {account.name} ({account.id})
            </option>
          ))}
        </select>
      </div>

      <div className="facebook-tabs">
        <button
          className={activeTab === 'campaigns' ? 'active' : ''}
          onClick={() => setActiveTab('campaigns')}
        >
          <Target size={16} />
          Campaigns ({campaigns.length})
        </button>
        <button
          className={activeTab === 'adsets' ? 'active' : ''}
          onClick={() => setActiveTab('adsets')}
        >
          <DollarSign size={16} />
          Ad Sets ({adSets.length})
        </button>
        <button
          className={activeTab === 'ads' ? 'active' : ''}
          onClick={() => setActiveTab('ads')}
        >
          <Calendar size={16} />
          Ads ({ads.length})
        </button>
      </div>

      {activeTab === 'campaigns' && (
        <>
          <div className="campaigns-header">
        <h3>
          <Target size={16} />
          Campaigns ({campaigns.length})
        </h3>
        <div className="flex gap-2">
          <button
            onClick={() => setShowCampaignWizard(true)}
            className="create-btn bg-blue-600 hover:bg-blue-700 text-white"
            disabled={!selectedAccount}
          >
            <Zap size={16} />
            Campaign Wizard
          </button>
          <button
            onClick={() => setShowCreateForm(!showCreateForm)}
            className="create-btn"
          >
            <Plus size={16} />
            {showCreateForm ? 'Cancel' : 'Quick Campaign'}
          </button>
        </div>
      </div>

      {showCreateForm && (
        <form onSubmit={handleSubmit(onCreateCampaign)} className="create-campaign-form">
          <h4>Create New Campaign</h4>
          
          <div className="form-group">
            <label>Campaign Name:</label>
            <input
              type="text"
              {...register('name', { required: 'Campaign name is required' })}
              placeholder="Enter campaign name"
            />
            {errors.name && <span className="error">{errors.name.message}</span>}
          </div>

          <div className="form-group">
            <label>Objective:</label>
            <select {...register('objective', { required: 'Objective is required' })}>
              <option value="">Select objective</option>
              {CAMPAIGN_OBJECTIVES.map((obj) => (
                <option key={obj.value} value={obj.value}>
                  {obj.label}
                </option>
              ))}
            </select>
            {errors.objective && <span className="error">{errors.objective.message}</span>}
          </div>

          <div className="form-group">
            <label>Special Ad Categories (optional):</label>
            <select {...register('specialAdCategories')}>
              <option value="">None</option>
              <option value="CREDIT">Credit</option>
              <option value="EMPLOYMENT">Employment</option>
              <option value="HOUSING">Housing</option>
              <option value="ISSUES_ELECTIONS_POLITICS">Issues, Elections or Politics</option>
            </select>
          </div>

          <div className="form-actions">
            <button type="submit" disabled={loading} className="submit-btn">
              {loading ? 'Creating...' : 'Create Campaign'}
            </button>
            <button 
              type="button" 
              onClick={() => setShowCreateForm(false)}
              className="cancel-btn"
            >
              Cancel
            </button>
          </div>
        </form>
      )}

      <div className="campaigns-list">
        {loading ? (
          <div className="loading">Loading campaigns...</div>
        ) : campaigns.length > 0 ? (
          campaigns.map((campaign) => (
            <div key={campaign.id} className="campaign-item">
              <div className="campaign-header">
                <h4>{campaign.name}</h4>
                <span className={`campaign-status ${campaign.status?.toLowerCase()}`}>
                  {campaign.status}
                </span>
              </div>
              <div className="campaign-details">
                <div className="detail-item">
                  <Target size={14} />
                  <span>Objective: {campaign.objective}</span>
                </div>
                <div className="detail-item">
                  <Calendar size={14} />
                  <span>Created: {new Date(campaign.created_time).toLocaleDateString()}</span>
                </div>
                {campaign.daily_budget && (
                  <div className="detail-item">
                    <DollarSign size={14} />
                    <span>Daily Budget: ${(campaign.daily_budget / 100).toFixed(2)}</span>
                  </div>
                )}
              </div>
            </div>
          ))
        ) : (
          <div className="no-campaigns">
            <p>No campaigns found for this ad account.</p>
            <p>Create your first campaign using the form above.</p>
          </div>
        )}
      </div>
        </>
      )}

      {activeTab === 'adsets' && (
        <div className="adsets-section">
          <h3>Ad Sets</h3>
          {loading ? (
            <div className="loading">Loading ad sets...</div>
          ) : adSets.length > 0 ? (
            adSets.map((adSet) => (
              <div key={adSet.id} className="adset-item">
                <div className="adset-header">
                  <strong>{adSet.name}</strong>
                  <span className={`status ${adSet.status?.toLowerCase()}`}>
                    {adSet.status}
                  </span>
                </div>
                <div className="adset-details">
                  <div className="detail-item">
                    <span>Campaign: {adSet.campaign_id}</span>
                  </div>
                  <div className="detail-item">
                    <Calendar size={14} />
                    <span>Created: {new Date(adSet.created_time).toLocaleDateString()}</span>
                  </div>
                  {adSet.daily_budget && (
                    <div className="detail-item">
                      <DollarSign size={14} />
                      <span>Daily Budget: ${(adSet.daily_budget / 100).toFixed(2)}</span>
                    </div>
                  )}
                  {adSet.optimization_goal && (
                    <div className="detail-item">
                      <Target size={14} />
                      <span>Optimization: {adSet.optimization_goal}</span>
                    </div>
                  )}
                </div>

                {/* Targeting Information */}
                <TargetingDisplay targeting={adSet.targeting_formatted} />
              </div>
            ))
          ) : (
            <div className="no-adsets">
              <p>No ad sets found for this ad account.</p>
            </div>
          )}
        </div>
      )}

      {activeTab === 'ads' && (
        <div className="ads-section">
          <h3>Ads</h3>
          {loading ? (
            <div className="loading">Loading ads...</div>
          ) : ads.length > 0 ? (
            ads.map((ad) => (
              <div key={ad.id} className="ad-item">
                <div className="ad-header">
                  <strong>{ad.name}</strong>
                  <span className={`status ${ad.status?.toLowerCase()}`}>
                    {ad.status}
                  </span>
                </div>
                <div className="ad-details">
                  <div className="detail-item">
                    <span>Ad Set: {ad.adset_id}</span>
                  </div>
                  <div className="detail-item">
                    <span>Campaign: {ad.campaign_id}</span>
                  </div>
                  <div className="detail-item">
                    <Calendar size={14} />
                    <span>Created: {new Date(ad.created_time).toLocaleDateString()}</span>
                  </div>
                  {ad.insights && ad.insights.impressions && ad.insights.impressions !== '0' && (
                    <div className="detail-item">
                      <span>Impressions: {parseInt(ad.insights.impressions).toLocaleString()}</span>
                    </div>
                  )}
                  {ad.insights && ad.insights.clicks && ad.insights.clicks !== '0' && (
                    <div className="detail-item">
                      <span>Clicks: {parseInt(ad.insights.clicks).toLocaleString()}</span>
                    </div>
                  )}
                  {ad.insights && ad.insights.spend && ad.insights.spend !== '0.00' && (
                    <div className="detail-item">
                      <DollarSign size={14} />
                      <span>Spend: ${parseFloat(ad.insights.spend).toFixed(2)}</span>
                    </div>
                  )}
                </div>

                {/* Creative Display */}
                <CreativeDisplay creative={ad.creative_formatted} />
              </div>
            ))
          ) : (
            <div className="no-ads">
              <p>No ads found for this ad account.</p>
            </div>
          )}
        </div>
      )}

      {/* Campaign Wizard Modal */}
      {showCampaignWizard && (
        <CampaignWizard
          adAccounts={adAccounts}
          selectedAccount={selectedAccount}
          onClose={() => setShowCampaignWizard(false)}
          onSuccess={handleWizardSuccess}
        />
      )}
    </div>
  );
};

export default CampaignSection;
