const axios = require('axios');
const config = require('../config/config');
const logger = require('../config/logger');
const redis = require('../config/redis');
const { ExternalServiceError, ValidationError } = require('../middleware/errorHandler');

class FacebookService {
  constructor() {
    this.baseUrl = config.facebook.baseUrl;
    this.apiVersion = config.facebook.apiVersion;
    this.appId = config.facebook.appId;
    this.appSecret = config.facebook.appSecret;
  }

  // Create axios instance with Facebook API configuration
  createApiClient(accessToken) {
    return axios.create({
      baseURL: `${this.baseUrl}/${this.apiVersion}`,
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });
  }

  // Generate OAuth URL for Facebook login
  generateOAuthUrl(redirectUri, state) {
    const permissions = [
      'ads_management',
      'ads_read',
      'business_management',
      'pages_read_engagement',
      'pages_manage_ads',
      'leads_retrieval'
    ].join(',');

    const params = new URLSearchParams({
      client_id: this.appId,
      redirect_uri: redirectUri,
      scope: permissions,
      response_type: 'code',
      state: state
    });

    return `https://www.facebook.com/v${this.apiVersion.substring(1)}/dialog/oauth?${params.toString()}`;
  }

  // Exchange authorization code for access token
  async exchangeCodeForToken(code, redirectUri) {
    try {
      const response = await axios.get(`${this.baseUrl}/oauth/access_token`, {
        params: {
          client_id: this.appId,
          client_secret: this.appSecret,
          redirect_uri: redirectUri,
          code: code
        }
      });

      return response.data;
    } catch (error) {
      logger.error('Facebook token exchange error:', error.response?.data || error.message);
      throw new ExternalServiceError('Facebook', 'Failed to exchange authorization code for token');
    }
  }

  // Get long-lived access token
  async getLongLivedToken(shortLivedToken) {
    try {
      const response = await axios.get(`${this.baseUrl}/oauth/access_token`, {
        params: {
          grant_type: 'fb_exchange_token',
          client_id: this.appId,
          client_secret: this.appSecret,
          fb_exchange_token: shortLivedToken
        }
      });

      return response.data;
    } catch (error) {
      logger.error('Facebook long-lived token error:', error.response?.data || error.message);
      throw new ExternalServiceError('Facebook', 'Failed to get long-lived token');
    }
  }

  // Get user profile information
  async getUserProfile(accessToken) {
    try {
      const api = this.createApiClient(accessToken);
      const response = await api.get('/me', {
        params: {
          fields: 'id,name,email,picture'
        }
      });

      return response.data;
    } catch (error) {
      logger.error('Facebook user profile error:', error.response?.data || error.message);
      throw new ExternalServiceError('Facebook', 'Failed to get user profile');
    }
  }

  // Get user's ad accounts
  async getAdAccounts(accessToken) {
    try {
      const api = this.createApiClient(accessToken);
      const response = await api.get('/me/adaccounts', {
        params: {
          fields: 'id,name,account_status,currency,timezone_name,business,account_id'
        }
      });

      return response.data.data || [];
    } catch (error) {
      logger.error('Facebook ad accounts error:', error.response?.data || error.message);
      throw new ExternalServiceError('Facebook', 'Failed to get ad accounts');
    }
  }

  // Get user's pages
  async getPages(accessToken) {
    try {
      const api = this.createApiClient(accessToken);
      const response = await api.get('/me/accounts', {
        params: {
          fields: 'id,name,category,access_token,picture'
        }
      });

      return response.data.data || [];
    } catch (error) {
      logger.error('Facebook pages error:', error.response?.data || error.message);
      throw new ExternalServiceError('Facebook', 'Failed to get pages');
    }
  }

  // Valid Facebook campaign objectives (as of 2024)
  static getValidObjectives() {
    return {
      // Recommended OUTCOME-based objectives
      recommended: [
        'OUTCOME_TRAFFIC',
        'OUTCOME_ENGAGEMENT',
        'OUTCOME_LEADS',
        'OUTCOME_SALES',
        'OUTCOME_AWARENESS',
        'OUTCOME_APP_PROMOTION'
      ],
      // Legacy objectives (still supported but deprecated)
      legacy: [
        'REACH',
        'CONVERSIONS',
        'BRAND_AWARENESS',
        'MESSAGES',
        'VIDEO_VIEWS',
        'STORE_VISITS',
        'APP_INSTALLS',
        'LEAD_GENERATION',
        'PAGE_LIKES'
      ]
    };
  }

  // Validate campaign objective
  validateObjective(objective) {
    const validObjectives = FacebookService.getValidObjectives();
    const allValid = [...validObjectives.recommended, ...validObjectives.legacy];

    if (!allValid.includes(objective)) {
      throw new ValidationError(
        `Invalid campaign objective: ${objective}. ` +
        `Recommended objectives: ${validObjectives.recommended.join(', ')}. ` +
        `Legacy objectives: ${validObjectives.legacy.join(', ')}`
      );
    }

    // Warn about legacy objectives
    if (validObjectives.legacy.includes(objective)) {
      logger.warn(`Using legacy objective: ${objective}. Consider using OUTCOME-based objectives for better performance.`);
    }
  }

  // Create a campaign
  async createCampaign(accessToken, adAccountId, campaignData) {
    try {
      // Validate objective before making API call
      this.validateObjective(campaignData.objective);

      const api = this.createApiClient(accessToken);
      const response = await api.post(`/act_${adAccountId}/campaigns`, {
        name: campaignData.name,
        objective: campaignData.objective,
        status: campaignData.status || 'PAUSED',
        special_ad_categories: campaignData.specialAdCategories || [],
        buying_type: campaignData.buyingType || 'AUCTION'
      });

      return response.data;
    } catch (error) {
      logger.error('Facebook campaign creation error:', error.response?.data || error.message);

      // Provide more helpful error messages for objective issues
      if (error.response?.data?.error?.message?.includes('Objective') &&
          error.response?.data?.error?.message?.includes('invalid')) {
        const validObjectives = FacebookService.getValidObjectives();
        throw new ExternalServiceError('Facebook',
          `Invalid campaign objective. Facebook now requires OUTCOME-based objectives. ` +
          `Try: ${validObjectives.recommended.join(', ')}`
        );
      }

      throw new ExternalServiceError('Facebook', 'Failed to create campaign');
    }
  }

  // Get campaigns for an ad account
  async getCampaigns(accessToken, adAccountId) {
    try {
      const api = this.createApiClient(accessToken);
      const response = await api.get(`/act_${adAccountId}/campaigns`, {
        params: {
          fields: 'id,name,objective,status,created_time,updated_time,effective_status,daily_budget,lifetime_budget'
        }
      });

      return response.data.data || [];
    } catch (error) {
      logger.error('Facebook campaigns fetch error:', error.response?.data || error.message);
      throw new ExternalServiceError('Facebook', 'Failed to get campaigns');
    }
  }

  // Create an ad set
  async createAdSet(accessToken, adAccountId, adSetData) {
    try {
      const api = this.createApiClient(accessToken);
      const response = await api.post(`/act_${adAccountId}/adsets`, {
        name: adSetData.name,
        campaign_id: adSetData.campaignId,
        daily_budget: adSetData.dailyBudget,
        billing_event: adSetData.billingEvent || 'IMPRESSIONS',
        optimization_goal: adSetData.optimizationGoal || 'REACH',
        bid_amount: adSetData.bidAmount,
        targeting: adSetData.targeting,
        status: adSetData.status || 'PAUSED'
      });

      return response.data;
    } catch (error) {
      logger.error('Facebook ad set creation error:', error.response?.data || error.message);
      throw new ExternalServiceError('Facebook', 'Failed to create ad set');
    }
  }

  // Create an ad
  async createAd(accessToken, adAccountId, adData) {
    try {
      const api = this.createApiClient(accessToken);
      const response = await api.post(`/act_${adAccountId}/ads`, {
        name: adData.name,
        adset_id: adData.adSetId,
        creative: adData.creative,
        status: adData.status || 'PAUSED'
      });

      return response.data;
    } catch (error) {
      logger.error('Facebook ad creation error:', error.response?.data || error.message);
      throw new ExternalServiceError('Facebook', 'Failed to create ad');
    }
  }

  // Get campaign insights
  async getCampaignInsights(accessToken, campaignId, dateRange = {}) {
    try {
      const api = this.createApiClient(accessToken);
      const params = {
        fields: 'impressions,clicks,spend,cpm,cpc,ctr,reach,frequency',
        time_range: dateRange.timeRange || JSON.stringify({
          since: dateRange.since || '2024-01-01',
          until: dateRange.until || new Date().toISOString().split('T')[0]
        })
      };

      const response = await api.get(`/${campaignId}/insights`, { params });
      return response.data.data || [];
    } catch (error) {
      logger.error('Facebook campaign insights error:', error.response?.data || error.message);
      throw new ExternalServiceError('Facebook', 'Failed to get campaign insights');
    }
  }

  // Get leads from a form
  async getLeads(accessToken, formId, options = {}) {
    try {
      const api = this.createApiClient(accessToken);
      const params = {
        fields: 'id,created_time,field_data',
        limit: options.limit || 100
      };

      if (options.after) {
        params.after = options.after;
      }

      const response = await api.get(`/${formId}/leads`, { params });
      return response.data;
    } catch (error) {
      logger.error('Facebook leads error:', error.response?.data || error.message);
      throw new ExternalServiceError('Facebook', 'Failed to get leads');
    }
  }

  // Subscribe to webhooks for lead forms
  async subscribeToWebhooks(accessToken, pageId, callbackUrl, verifyToken) {
    try {
      const api = this.createApiClient(accessToken);
      const response = await api.post(`/${pageId}/subscribed_apps`, {
        subscribed_fields: 'leadgen',
        callback_url: callbackUrl,
        verify_token: verifyToken
      });

      return response.data;
    } catch (error) {
      logger.error('Facebook webhook subscription error:', error.response?.data || error.message);
      throw new ExternalServiceError('Facebook', 'Failed to subscribe to webhooks');
    }
  }

  // Validate webhook signature
  validateWebhookSignature(payload, signature) {
    const crypto = require('crypto');
    const expectedSignature = crypto
      .createHmac('sha256', this.appSecret)
      .update(payload)
      .digest('hex');

    return `sha256=${expectedSignature}` === signature;
  }

  // Cache frequently accessed data
  async cacheAdAccounts(userId, adAccounts) {
    const cacheKey = `facebook:ad_accounts:${userId}`;
    await redis.set(cacheKey, adAccounts, config.cache.shortTtl);
  }

  async getCachedAdAccounts(userId) {
    const cacheKey = `facebook:ad_accounts:${userId}`;
    return await redis.get(cacheKey);
  }

  // Rate limiting helpers
  async checkRateLimit(userId) {
    const key = `facebook_api_rate_limit:${userId}`;
    const current = await redis.incrementRateLimit(key, 3600); // 1 hour window
    
    // Facebook API allows 200 calls per hour per user
    if (current > 200) {
      throw new ExternalServiceError('Facebook', 'Rate limit exceeded for Facebook API');
    }

    return current;
  }

  // Health check for Facebook API
  async healthCheck(accessToken) {
    try {
      const api = this.createApiClient(accessToken);
      await api.get('/me', { params: { fields: 'id' } });
      return { status: 'healthy', timestamp: new Date().toISOString() };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

module.exports = new FacebookService();
