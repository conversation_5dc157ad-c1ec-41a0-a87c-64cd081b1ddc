const axios = require('axios');

async function testCampaignHierarchy() {
  try {
    console.log('🧪 Testing Campaign Hierarchy Creation...');
    
    const testData = {
      adAccountId: 'act_911817533003697', // Using a valid ad account ID from the logs
      campaign: {
        name: 'Test Hierarchy Campaign',
        objective: 'OUTCOME_TRAFFIC',
        status: 'PAUSED'
      },
      adSet: {
        name: 'Test Hierarchy Ad Set',
        dailyBudget: '15',
        targeting: {
          age_min: 25,
          age_max: 45,
          genders: [0], // All genders
          geo_locations: {
            countries: ['US']
          }
        },
        optimizationGoal: 'LINK_CLICKS',
        billingEvent: 'LINK_CLICKS',
        bidStrategy: 'LOWEST_COST_WITHOUT_CAP',
        status: 'PAUSED'
      },
      ad: {
        name: 'Test Hierarchy Ad',
        creative: {
          object_story_spec: {
            page_id: '*********', // This would need to be a real page ID
            link_data: {
              link: 'https://example.com',
              message: 'Check out our amazing product! Limited time offer.',
              name: 'Amazing Product',
              description: 'The best product you will ever find.',
              call_to_action: {
                type: 'LEARN_MORE',
                value: {
                  link: 'https://example.com'
                }
              }
            }
          }
        },
        status: 'PAUSED'
      }
    };

    console.log('📝 Test Data:', JSON.stringify(testData, null, 2));
    
    const response = await axios.post('http://localhost:3000/api/v1/facebook/campaign-hierarchy', testData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Campaign Hierarchy Created Successfully!');
    console.log('📊 Response:', JSON.stringify(response.data, null, 2));
    
    if (response.data.entities) {
      console.log('\n🎯 Created Entities:');
      console.log(`Campaign ID: ${response.data.entities.campaign?.id}`);
      console.log(`Ad Set ID: ${response.data.entities.adSet?.id}`);
      console.log(`Ad ID: ${response.data.entities.ad?.id}`);
    }

  } catch (error) {
    console.error('❌ Campaign Hierarchy Creation Failed:');
    console.error('Error:', error.response?.data || error.message);
    
    if (error.response?.data?.details) {
      console.error('Details:', error.response.data.details);
    }
  }
}

// Test individual endpoints first
async function testEndpoints() {
  try {
    console.log('🧪 Testing Individual Endpoints...\n');
    
    // Test targeting options
    console.log('1. Testing targeting options...');
    const targetingRes = await axios.get('http://localhost:3000/api/v1/facebook/targeting-options');
    console.log('✅ Targeting options loaded:', Object.keys(targetingRes.data));
    
    // Test creative templates
    console.log('2. Testing creative templates...');
    const creativeRes = await axios.get('http://localhost:3000/api/v1/facebook/creative-templates');
    console.log('✅ Creative templates loaded:', Object.keys(creativeRes.data));
    
    // Test campaign objectives
    console.log('3. Testing campaign objectives...');
    const objectivesRes = await axios.get('http://localhost:3000/api/v1/facebook/campaign-objectives');
    console.log('✅ Campaign objectives loaded:', objectivesRes.data.recommended?.length || 0, 'recommended');
    
    console.log('\n🎉 All endpoints working correctly!\n');
    
  } catch (error) {
    console.error('❌ Endpoint test failed:', error.message);
    return false;
  }
  
  return true;
}

async function main() {
  console.log('🚀 Starting Campaign Hierarchy Tests\n');
  
  // Test endpoints first
  const endpointsWorking = await testEndpoints();
  
  if (endpointsWorking) {
    // Test full hierarchy creation
    await testCampaignHierarchy();
  }
  
  console.log('\n✨ Test completed!');
}

main().catch(console.error);
