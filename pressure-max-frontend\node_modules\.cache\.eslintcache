[{"C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\AuthSection.js": "3", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignSection.js": "4", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookSection.js": "5", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\ApiTestingSection.js": "6", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\AuthContext.js": "7", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\services\\api.js": "8", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookConnectionSection.js": "9", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\FacebookContext.js": "10", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\TargetingDisplay.js": "11", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CreativeDisplay.js": "12", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignWizard.js": "13"}, {"size": 232, "mtime": 1750870627179, "results": "14", "hashOfConfig": "15"}, {"size": 2140, "mtime": 1750906675864, "results": "16", "hashOfConfig": "15"}, {"size": 10186, "mtime": 1750904956981, "results": "17", "hashOfConfig": "15"}, {"size": 16564, "mtime": 1750924710544, "results": "18", "hashOfConfig": "15"}, {"size": 7324, "mtime": 1750904956983, "results": "19", "hashOfConfig": "15"}, {"size": 8243, "mtime": 1750873838649, "results": "20", "hashOfConfig": "15"}, {"size": 3438, "mtime": 1750870723227, "results": "21", "hashOfConfig": "15"}, {"size": 4079, "mtime": 1750906474408, "results": "22", "hashOfConfig": "15"}, {"size": 4531, "mtime": 1750906642416, "results": "23", "hashOfConfig": "15"}, {"size": 4881, "mtime": 1750906513515, "results": "24", "hashOfConfig": "15"}, {"size": 15521, "mtime": 1750913282515, "results": "25", "hashOfConfig": "15"}, {"size": 11191, "mtime": 1750915070114, "results": "26", "hashOfConfig": "15"}, {"size": 18655, "mtime": 1750924600930, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1klh60u", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\AuthSection.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignSection.js", ["67", "68", "69"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookSection.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\ApiTestingSection.js", ["70", "71"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\AuthContext.js", ["72"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookConnectionSection.js", ["73", "74"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\FacebookContext.js", ["75"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\TargetingDisplay.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CreativeDisplay.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignWizard.js", ["76", "77"], [], {"ruleId": "78", "severity": 1, "message": "79", "line": 2, "column": 8, "nodeType": "80", "messageId": "81", "endLine": 2, "endColumn": 11}, {"ruleId": "82", "severity": 1, "message": "83", "line": 65, "column": 6, "nodeType": "84", "endLine": 65, "endColumn": 23, "suggestions": "85"}, {"ruleId": "82", "severity": 1, "message": "83", "line": 76, "column": 6, "nodeType": "84", "endLine": 76, "endColumn": 46, "suggestions": "86"}, {"ruleId": "78", "severity": 1, "message": "87", "line": 8, "column": 28, "nodeType": "80", "messageId": "81", "endLine": 8, "endColumn": 32}, {"ruleId": "82", "severity": 1, "message": "88", "line": 22, "column": 6, "nodeType": "84", "endLine": 22, "endColumn": 23, "suggestions": "89"}, {"ruleId": "82", "severity": 1, "message": "90", "line": 22, "column": 6, "nodeType": "84", "endLine": 22, "endColumn": 8, "suggestions": "91"}, {"ruleId": "78", "severity": 1, "message": "92", "line": 5, "column": 20, "nodeType": "80", "messageId": "81", "endLine": 5, "endColumn": 25}, {"ruleId": "82", "severity": 1, "message": "93", "line": 25, "column": 6, "nodeType": "84", "endLine": 25, "endColumn": 32, "suggestions": "94"}, {"ruleId": "82", "severity": 1, "message": "95", "line": 24, "column": 6, "nodeType": "84", "endLine": 24, "endColumn": 8, "suggestions": "96"}, {"ruleId": "78", "severity": 1, "message": "97", "line": 10, "column": 3, "nodeType": "80", "messageId": "81", "endLine": 10, "endColumn": 11}, {"ruleId": "78", "severity": 1, "message": "98", "line": 23, "column": 42, "nodeType": "80", "messageId": "81", "endLine": 23, "endColumn": 50}, "no-unused-vars", "'api' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadCampaigns'. Either include it or remove the dependency array.", "ArrayExpression", ["99"], ["100"], "'user' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checkServerHealth' and 'loadUserProfile'. Either include them or remove the dependency array.", ["101"], "React Hook useEffect has a missing dependency: 'checkAuthStatus'. Either include it or remove the dependency array.", ["102"], "'Users' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadAdAccounts'. Either include it or remove the dependency array.", ["103"], "React Hook useEffect has missing dependencies: 'checkFacebookAuth' and 'handleOAuthCallback'. Either include them or remove the dependency array.", ["104"], "'Calendar' is defined but never used.", "'setValue' is assigned a value but never used.", {"desc": "105", "fix": "106"}, {"desc": "107", "fix": "108"}, {"desc": "109", "fix": "110"}, {"desc": "111", "fix": "112"}, {"desc": "113", "fix": "114"}, {"desc": "115", "fix": "116"}, "Update the dependencies array to be: [loadCampaigns, selectedAccount]", {"range": "117", "text": "118"}, "Update the dependencies array to be: [activeTab, selectedAccount, loadedData, loadCampaigns]", {"range": "119", "text": "120"}, "Update the dependencies array to be: [checkServerHealth, isAuthenticated, loadUserProfile]", {"range": "121", "text": "122"}, "Update the dependencies array to be: [checkAuthStatus]", {"range": "123", "text": "124"}, "Update the dependencies array to be: [isConnected, accessToken, loadAdAccounts]", {"range": "125", "text": "126"}, "Update the dependencies array to be: [checkFacebookAuth, handleOAuthCallback]", {"range": "127", "text": "128"}, [2425, 2442], "[load<PERSON><PERSON>ai<PERSON><PERSON>, selectedAccount]", [2850, 2890], "[activeTab, selectedAccount, loadedData, loadCampaigns]", [722, 739], "[checkServerHealth, isAuthenticated, loadUserProfile]", [636, 638], "[checkAuthStatus]", [724, 750], "[isConnected, accessToken, loadAdAccounts]", [762, 764], "[checkFacebookAuth, handleOAuthCallback]"]