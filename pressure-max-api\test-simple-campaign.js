const axios = require('axios');

async function testSimpleCampaign() {
  try {
    console.log('🧪 Testing Simple Campaign Creation...');
    
    const campaignData = {
      adAccountId: 'act_911817533003697',
      name: 'Simple Test Campaign',
      objective: 'OUTCOME_TRAFFIC',
      status: 'PAUSED'
    };

    console.log('📝 Creating campaign:', campaignData);
    
    const response = await axios.post('http://localhost:3000/api/v1/facebook/campaigns', campaignData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Campaign Created Successfully!');
    console.log('📊 Response:', JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.error('❌ Campaign Creation Failed:');
    console.error('Error:', error.response?.data || error.message);
  }
}

testSimpleCampaign().catch(console.error);
